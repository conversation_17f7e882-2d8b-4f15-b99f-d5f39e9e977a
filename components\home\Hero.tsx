'use client';

import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className=" h-screen w-full overflow-hidden relative-flex">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-20"
      />

      <div className="col-span-12 lg:col-span-8 xl:col-span-7 pt-[5%] md:pt-6 sm:pt-10 md:pl-[12%] pl-[2%]">
            <div className="flex items-center gap-3">
              <img
                src="/logo-icon.png"
                alt="Logo Icon"
                className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
              />
              <div>
                <h1 className="text-white font-semibold text-lg sm:text-xl md:text-2xl leading-tight">
                  Prolytech
                </h1>
                <p className="text-[#05A0E2] text-[5px] sm:text-[5.5px] md:text-[6px] font-bold tracking-wide uppercase">
                  DEFINING THE CURVE OF WHAT'S NEXT
                </p>
              </div>
            </div>
          </div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/80 -z-10 " />

      {/* Main Container with Grid Layout */}
      <div className=" z-10 h-full w-full">
        {/* Mobile Layout - Content at bottom */}
        <div className="md:hidden flex flex-col h-full">
          {/* Video space at top - takes up remaining space */}
          <div className="flex-1"></div>

          {/* Content container at bottom */}
          <div className="px-[5%] pb-[25%] space-y-6">
            {/* Main Content Section */}
            <div>
              {/* Main Heading */}
              <h1 className="text-white text-2xl font-semibold leading-snug">
                <span className="block mb-2">Powering the Next Wave</span>
                <span className="flex flex-wrap items-center gap-2">
                  <span>of</span>
                  <TypewriterEffect
                    phrases={[
                      'Digital Innovation',
                      'Intelligent Automation',
                      'Scalable Growth',
                      'Cloud Scalability'
                    ]}
                    className="text-cyan-400"
                    typingSpeed={50}
                    deletingSpeed={25}
                    pauseDuration={2500}
                  />
                </span>
              </h1>

              {/* Description */}
              <p className="text-gray-200 text-sm mt-4 leading-relaxed">
                We architect and deliver high-performance platforms—social, transactional, and intelligent—at startup speed and enterprise scale.
              </p>

              {/* CTA Button */}
              <Link
                href="/contact"
                className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm rounded-full shadow-md transition-all duration-300
                bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)] w-full text-center"
              >
                Schedule a Consultation
              </Link>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Original layout for md and up */}
        <div className="hidden md:grid grid-cols-12 h-full">
          {/* Header/Logo Section - Aligned to grid
          <div className="col-span-12 lg:col-span-8 xl:col-span-7 pt-[6%] md:pt-6 sm:pt-8 md:pl-[21%] pl-[2%]">
            <div className="flex items-center gap-3">
              <img
                src="/logo-icon.png"
                alt="Logo Icon"
                className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
              />
              <div>
                <h1 className="text-white font-semibold text-lg sm:text-xl md:text-2xl leading-tight">
                  Prolytech
                </h1>
                <p className="text-[#05A0E2] text-[5px] sm:text-[5.5px] md:text-[6px] font-bold tracking-wide uppercase">
                  DEFINING THE CURVE OF WHAT'S NEXT
                </p>
              </div>
            </div>
          </div> */}

          {/* Main Content Section - Starts from 3rd grid column */}
          <div className="absolute bottom-[5%] md:bottom-[20%] col-span-12 sm:col-end lg:col-span-8 xl:col-span-7 pt-6 md:pl-[13%] pl-[5%]">
            {/* Main Heading */}
            <h1 className="text-white text-2xl sm:text-3xl md:text-[46px] font-semibold leading-snug">
              <span className="block mb-2">Powering the Next Wave</span>
              <span className="flex flex-wrap items-center gap-2">
                <span>of</span>
                <TypewriterEffect
                  phrases={[
                    'Digital Innovation',
                    'Intelligent Automation',
                    'Scalable Growth',
                    'Cloud Scalability'
                  ]}
                  className="text-cyan-400"
                  typingSpeed={50}
                  deletingSpeed={25}
                  pauseDuration={2500}
                />
              </span>
            </h1>

            {/* Description */}
            <p className="text-gray-200 text-sm md:text-base mt-4 leading-relaxed max-w-md md:max-w-none">
              We architect and deliver high-performance platforms—social, transactional,<br/>
              and intelligent—at startup speed and enterprise scale.
            </p>

            {/* CTA Button */}
            <Link
              href="/contact"
              className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm md:text-base rounded-full shadow-md transition-all duration-300
              bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]"
            >
              Schedule a Consultation
            </Link>
          </div>

          {/* Right spacing column */}
          <div className="hidden xl:block xl:col-span-2"></div>
        </div>
      </div>
    </section>
  );
}