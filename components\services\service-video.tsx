import AboutBg from '../icons/aboutbg';

const ServiceVideo = () => (
  <div className="flex-1 flex justify-center lg:justify-end items-center absolute w-full lg:w-auto mt-15 lg:mt-8 order-1 lg:order-2 lg:ml-[55%]">
    <div className="absolute z-0
      top-1/2 left-1/2 transform
      scale-[0.8] sm:scale-[1] lg:scale-[1.3] xl:scale-[1.4]
      -translate-y-[calc(40%+40px)] -translate-x-[calc(60%-10px)]">
      <AboutBg className="w-[330px] h-[360px] lg:w-[280px] lg:h-[290px] text-blue-500 lg:ml-10" />
    </div>

    <div
      className="relative z-10 w-full max-w-[290px] sm:max-w-[390px] h-[300px] sm:h-[410px] overflow-hidden"
      style={{
        clipPath: "url(#blobClip)",
        WebkitClipPath: "url(#blobClip)",
      }}
    >
      <div
        className="w-full h-full overflow-hidden"
        style={{
          clipPath: "url(#blobClip)",
          WebkitClipPath: "url(#blobClip)",
        }}
      >
        <video
          src="/assets/services-video.mp4"
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  </div>
);

export default ServiceVideo;