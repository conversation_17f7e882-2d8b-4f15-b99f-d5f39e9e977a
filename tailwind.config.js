// /** @type {import('tailwindcss').Config} */
// module.exports = {
//   content: [
//     './app/**/*.{js,ts,jsx,tsx}',
//     './pages/**/*.{js,ts,jsx,tsx}',
//     './components/**/*.{js,ts,jsx,tsx}',
//   ],
//   theme: {
//     extend: {
//       fontFamily: {
//         'inter': ['var(--font-inter)', 'sans-serif'],
//       },
//     },
//   },
// plugins: [require('@tailwindcss/line-clamp')],
// };



/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // Ensure these paths correctly point to your source files
    './app/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      // Your custom font family definition
      fontFamily: {
        'inter': ['var(--font-inter)', 'sans-serif'],
      },
    },
  },
  plugins: [
    // Your valid Tailwind CSS plugins
    require('@tailwindcss/line-clamp'),
  ],
};