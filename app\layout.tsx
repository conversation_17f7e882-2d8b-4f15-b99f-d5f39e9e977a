import './globals.css';
import { Inter } from 'next/font/google';
import Navbar from '@/components/home/<USER>';

const inter = Inter({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-inter',
});

export const metadata = {
  title: 'Prolytech',
  description: 'Digital Innovation Platform',
  icons: {
    icon: '/favicon.png',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-inter">
        {/* ClipPath SVG used globally */}
        <svg width="0" height="0" style={{ position: 'absolute' }}>
          <clipPath id="video-clip" clipPathUnits="objectBoundingBox">
            <path d="M0.772,0.013 C0.905,0.111,1,0.315,0.967,0.495 C0.934,0.674,0.773,0.828,0.605,0.906 C0.437,0.984,0.262,0.985,0.12,0.912 C-0.022,0.839,-0.028,0.693,0.02,0.545 C0.069,0.397,0.172,0.247,0.3,0.137 C0.428,0.028,0.639,-0.086,0.772,0.013 Z" />
          </clipPath>
        </svg>

        <Navbar />
        {children}
      </body>
    </html>
  );
}
