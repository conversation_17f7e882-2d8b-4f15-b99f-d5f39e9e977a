'use client';

import React from 'react';
import { values } from './values';

export const ValuesSection: React.FC = () => {
    return (
        <section className="bg-black w-full md:px-8 lg:px-20 py-8 md:py-12 lg:py-16">
            <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-start gap-6 md:gap-8 text-white">
                <div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
                    <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">
                        Our Principles
                    </p>
                    <h2 className="text-lg sm:text-4xl md:text-[24px] lg:text-[28px] font-normal text-white leading-[1.2]">
                        Values We Live By
                    </h2>
                </div>
                <div className="flex-1 max-w-lg space-y-7 md:space-y-8">
                    {values.map((item, index) => (
                        <div key={index} className="flex items-start gap-3 md:gap-4">
                            <div className="text-[#05A0E2] text-xl md:text-2xl mt-1 leading-[22px] md:leading-[28px]">–</div>
                            <div>
                                <h3 className="font-semibold text-white text-[16px] md:text-[18px] leading-[22px] md:leading-[26px]">
                                    {item.title}
                                </h3>
                                <p className="text-gray-600 text-sm md:text-sm mt-2 md:mt-1 leading-relaxed">
                                    {item.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};