"use client";

import Link from "next/link";
import { Mail, Phone } from "lucide-react";
import { FC } from "react";

const CTABanner: FC = () => {
  return (
    <section className="relative bg-gradient-to-r from-[#4A90E2] to-[#1E80FF] text-white text-center py-16 px-6 overflow-hidden md:pl-[140px] lg:pl-[50px]">

      <h2
        className="text-[32px] font-normal leading-[40px] tracking-[0%] mb-4 relative z-20"
        style={{ fontFamily: "Inter", color: "#FFFFFF" }}
      >
        Let's Build Your Digital Ecosystem
      </h2>

      <p
        className="max-w-[90%] sm:max-w-2xl mx-auto mb-6 relative z-20 text-[18px] font-normal leading-[24.5px] tracking-[0%] text-center"
        style={{ fontFamily: "Inter", color: "rgba(255, 255, 255, 0.9)" }}
      >
        Looking to build a custom software solution, a mobile platform, a
        <br className="hidden sm:block" />
        marketplace, or an AI-powered app? Let's partner.
      </p>

      <div className="flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6 mb-6 relative z-20">
        <div
          className="flex items-center gap-2 text-[18px] font-normal leading-[24.5px] tracking-[0%]"
          style={{ fontFamily: "Inter", color: "rgba(255, 255, 255, 0.9)" }}
        >
          <Mail size={16} />
          <span><EMAIL></span>
        </div>
        <div
          className="flex items-center gap-2 text-[18px] font-normal leading-[24.5px] tracking-[0%]"
          style={{ fontFamily: "Inter", color: "rgba(255, 255, 255, 0.9)" }}
        >
          <Phone size={16} />
          <span>+91-8247532770</span>
        </div>
      </div>

      <Link
        href="/contact"
        className="inline-flex items-center justify-center gap-2 bg-white text-[#4A90E2] rounded-full relative z-20"
        style={{
          fontFamily: "Inter",
          fontSize: "14px",
          fontWeight: "400",
          lineHeight: "17.5px",
          letterSpacing: "0%",
          width: "168px",
          height: "42px",
        }}
      >
        Get Started Now
        <span className="text-lg">→</span>
      </Link>
    </section>
  );
};

export default CTABanner;
