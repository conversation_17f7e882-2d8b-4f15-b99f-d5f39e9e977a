'use client';

import Link from 'next/link';
import ServiceVideo from './service-video';

const ServiceHero = () => {
  return (
    <div className="w-full min-h-screen bg-white relative flex">

      <div className="absolute inset-0 z-0 bg-[url('/grid.png')] bg-repeat opacity-60"></div>

      {/* Navbar - Hidden on small and medium screens, visible on large screens */}
      <div className="fixed left-0 top-0 h-full w-16 bg-black flex-col items-center justify-center space-y-8 z-10 hidden lg:flex">
        <div className="w-6 h-6 bg-cyan-400 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
      </div>

      {/* Main Content Area - Adjusted margin for small/medium screens */}
      <div className="flex-1 ml-0 lg:ml-16 bg-transparent relative overflow-hidden">

        {/* Logo - Responsive positioning to match home page */}
        <div className="mx-auto pl-[5%]">
      <div className="flex items-center gap-2 mt-5">
        <img
          src="/logo-icon.png"
          alt="Logo Icon"
          className="w-10 h-10"
        />
        <div>
          <h1 className="text-black font-bold text-xl md:text-2xl leading-tight">
            Prolytech
          </h1>
          <p className="text-[#05A0E2] text-[5.5px] font-bold tracking-wide">
            DEFINING THE CURVE OF WHAT'S NEXT
          </p>
        </div>
      </div>
    </div>

        {/* SVG clipPath for the blob, necessary for the video */}
        <svg width="0" height="0">
          <defs>
            <clipPath
              id="blobClip"
              clipPathUnits="objectBoundingBox"
              transform="scale(0.0017, 0.0016)"
            >
              <path d="M81.5702 435.071C122.524 497.307 132.82 569.329 199.46 602.679C276.32 641.145 332.063 562.008 372.109 530.182C404.709 504.272 539.123 512.181 568.025 482.205C616.135 432.309 560.186 383.738 538.312 317.98C521.208 266.562 542.548 186.363 523.087 135.788C495.179 63.2584 475.03 13.9755 398.118 2.63276C332.931 -6.98075 307.096 10.7407 247.773 39.409C176.483 73.8611 73.3626 93.2439 31.9879 160.73C-0.113567 213.091 -10.6617 261.071 12.4247 317.98C33.709 370.448 50.4438 387.769 81.5702 435.071Z" />
            </clipPath>
          </defs>
        </svg>

        {/* Hero Content */}
        {/* Mobile Layout - Content at bottom */}
        <div className="lg:hidden relative z-10 h-screen flex flex-col">
          {/* Video area - takes up space but video positioned absolutely */}
          <div className="flex-1 relative">
            <ServiceVideo/>
          </div>

          {/* Left Content at bottom */}

          <div className="absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm pl-[5%] pb-[25%] bg-[url('/grid.png')] bg-repeat">
            <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">
              Our Expertise
            </p>

            <h1 className="text-black text-2xl sm:text-3xl font-extrabold leading-tight">
              <span className="block">Software, Cloud &</span>
              <span className="block">AI Solutions</span>
            </h1>

            <p className="text-gray-600 text-sm mt-4 leading-relaxed">
              Explore how Prolytech engineers high-performance applications, cloud systems, and
              AI-powdered platforms for scale and innovation.
            </p>

            
          </div>
        </div>

        {/* Desktop Layout - Original layout for lg and up */}
        <div className="hidden lg:flex relative z-10 flex-row items-center justify-between h-screen pl-[5%] pr-[5%] py-0 pb-[10%]">
          {/* Left Content */}
          <div className="flex flex-col items-start justify-end text-left pb-[3%] mt-[10%] max-w-2xl order-1">
            <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">
              Our Expertise
            </p>

            <h1 className="text-black text-4xl lg:text-5xl font-extrabold leading-tight">
              <span className="block">Software, Cloud &</span>
              <span className="block">AI Solutions</span>
            </h1>

            <p className="text-gray-600 text-sm mt-4 leading-relaxed">
              Explore how Prolytech engineers high-performance applications, cloud systems, and <br/>
              AI-powdered platforms for scale and innovation.
            </p>
            
          </div>

          {/* Right Side Video with Blob Shape and Background SVG */}
          <ServiceVideo/>
        </div>
      </div>
    </div>
  );
};

export default ServiceHero;